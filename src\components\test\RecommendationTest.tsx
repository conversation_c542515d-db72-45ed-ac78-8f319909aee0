import React, { useState, useEffect } from 'react';
import { RecommendationService } from '@/services/recommendationService';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const RecommendationTest: React.FC = () => {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const runTest = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Testing recommendation service with user:', user);
      
      // Test personalized recommendations
      const personalizedResult = await RecommendationService.getPersonalizedRecommendations(user, 3);
      console.log('Personalized recommendations result:', personalizedResult);
      
      // Test trending blogs
      const trendingBlogs = await RecommendationService.getTrendingBlogs(3);
      console.log('Trending blogs result:', trendingBlogs);
      
      // Test practice area content check
      const hasPracticeAreaContent = user ? 
        await RecommendationService.hasPracticeAreaContent(user.practiceArea) : false;
      console.log('Has practice area content:', hasPracticeAreaContent);
      
      // Test available practice areas
      const availablePracticeAreas = await RecommendationService.getAvailablePracticeAreas();
      console.log('Available practice areas:', availablePracticeAreas);
      
      setTestResults({
        personalizedResult,
        trendingBlogs,
        hasPracticeAreaContent,
        availablePracticeAreas,
        user: user ? {
          id: user.id,
          email: user.email,
          practiceArea: user.practiceArea,
          fullName: user.fullName
        } : null
      });
      
    } catch (err) {
      console.error('Test error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto m-4">
      <CardHeader>
        <CardTitle>Recommendation System Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-4">
          <Button onClick={runTest} disabled={loading}>
            {loading ? 'Testing...' : 'Run Test'}
          </Button>
          <div className="text-sm text-gray-600">
            User logged in: {user ? 'Yes' : 'No'}
            {user && ` (${user.practiceArea})`}
          </div>
        </div>
        
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <h3 className="font-semibold text-red-800">Error:</h3>
            <p className="text-red-700">{error}</p>
          </div>
        )}
        
        {testResults && (
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-2">User Info:</h3>
              <pre className="text-sm text-blue-700 overflow-auto">
                {JSON.stringify(testResults.user, null, 2)}
              </pre>
            </div>
            
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-semibold text-green-800 mb-2">Personalized Recommendations:</h3>
              <p className="text-sm text-green-700 mb-2">
                Type: {testResults.personalizedResult.recommendationType} | 
                Count: {testResults.personalizedResult.blogs.length} | 
                Message: {testResults.personalizedResult.message}
              </p>
              <pre className="text-xs text-green-600 overflow-auto max-h-40">
                {JSON.stringify(testResults.personalizedResult.blogs.map((blog: any) => ({
                  id: blog.content_id,
                  title: blog.title,
                  category: blog.category,
                  engagement_score: blog.engagement_score,
                  likes: blog.likes,
                  comments: blog.comment_count
                })), null, 2)}
              </pre>
            </div>
            
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <h3 className="font-semibold text-purple-800 mb-2">Trending Blogs:</h3>
              <p className="text-sm text-purple-700 mb-2">Count: {testResults.trendingBlogs.length}</p>
              <pre className="text-xs text-purple-600 overflow-auto max-h-40">
                {JSON.stringify(testResults.trendingBlogs.map((blog: any) => ({
                  id: blog.content_id,
                  title: blog.title,
                  category: blog.category,
                  engagement_score: blog.engagement_score,
                  likes: blog.likes,
                  comments: blog.comment_count
                })), null, 2)}
              </pre>
            </div>
            
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-semibold text-yellow-800 mb-2">Practice Area Analysis:</h3>
              <p className="text-sm text-yellow-700 mb-2">
                Has content for user's practice area: {testResults.hasPracticeAreaContent ? 'Yes' : 'No'}
              </p>
              <p className="text-sm text-yellow-700 mb-2">
                Available practice areas: {testResults.availablePracticeAreas.length}
              </p>
              <pre className="text-xs text-yellow-600 overflow-auto max-h-32">
                {JSON.stringify(testResults.availablePracticeAreas, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RecommendationTest;
